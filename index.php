<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
require __DIR__ . '/vendor/autoload.php';

// Check if credentials file exists
$credentialsFile = __DIR__ . '/odzleads-3fd4a44b7310.json';
if (!file_exists($credentialsFile)) {
    die("❌ Error: Google Service Account credentials file 'odzleads-3fd4a44b7310.json' not found!<br><br>
    <strong>To fix this:</strong><br>
    1. Go to <a href='https://console.cloud.google.com/' target='_blank'>Google Cloud Console</a><br>
    2. Create or select a project<br>
    3. Enable the Google Sheets API<br>
    4. Create a Service Account<br>
    5. Download the JSON credentials file<br>
    6. Place it in your project root directory as 'odzleads-3fd4a44b7310.json'<br><br>
    <strong>Current directory:</strong> " . __DIR__);
}

$client = new \Google_Client();
$client->setApplicationName('Google Sheets API with PHP');
$client->setScopes([\Google_Service_Sheets::SPREADSHEETS]);
$client->setAuthConfig($credentialsFile);
$client->setAccessType('offline');

$service = new Google_Service_Sheets($client);

// Spreadsheet details
$spreadsheetId = 'YOUR_SPREADSHEET_ID_HERE'; // Replace with your actual Google Sheet ID

// Check if spreadsheet ID is set
if ($spreadsheetId === 'YOUR_SPREADSHEET_ID_HERE' || empty($spreadsheetId)) {
    die("❌ Error: Please set your Google Sheet ID!<br><br>
    <strong>To fix this:</strong><br>
    1. Open your Google Sheet<br>
    2. Copy the ID from the URL (the long string between '/d/' and '/edit')<br>
    3. Replace 'YOUR_SPREADSHEET_ID_HERE' on line 29 with your actual Sheet ID<br><br>
    <strong>Example:</strong> If your URL is https://docs.google.com/spreadsheets/d/1ABC123xyz/edit<br>
    Then your Sheet ID is: 1ABC123xyz");
}

$sheetName = 'Sheet1';
$range = $sheetName . '!F2:M'; // Read F (status) and M (date)

$response = $service->spreadsheets_values->get($spreadsheetId, $range);
$rows = $response->getValues();

$today = new DateTime();
$cutoffDate = (clone $today)->modify('-7 days');

foreach ($rows as $index => $row) {
    $status = strtolower(trim($row[0] ?? ''));
    $dateRaw = trim($row[7] ?? ''); // Column M is index 7 in range F2:M (F=0, G=1, H=2, I=3, J=4, K=5, L=6, M=7)

    if ($status !== 'na' || empty($dateRaw)) {
        continue;
    }

    // Clean hidden characters and extra whitespace
    $dateRaw = preg_replace('/[\x00-\x1F\x7F\xA0]/u', '', $dateRaw);

    // Try multiple formats
    $date = DateTime::createFromFormat('m/d/Y', $dateRaw) ?:
            DateTime::createFromFormat('d/m/Y', $dateRaw) ?:
            DateTime::createFromFormat('Y-m-d', $dateRaw);

    if (!$date) {
        echo "❌ Could not parse date in row " . ($index + 2) . ": [$dateRaw]\n";
        continue;
    }

    // For debug
    echo "🔍 Row " . ($index + 2) . ": Parsed Date = " . $date->format('Y-m-d') . " | Cutoff = " . $cutoffDate->format('Y-m-d') . "\n";

    if ($date <= $cutoffDate) {
        $rowNumber = $index + 2;
        $targetRange = "$sheetName!F$rowNumber";

        $valueRange = new Google_Service_Sheets_ValueRange([
            'range' => $targetRange,
            'values' => [['Not Converted']]
        ]);

        $params = ['valueInputOption' => 'RAW'];
        $service->spreadsheets_values->update($spreadsheetId, $targetRange, $valueRange, $params);

        echo "✅ Updated row $rowNumber: 'NA' -> 'Not Converted'\n";
    }
}
?>