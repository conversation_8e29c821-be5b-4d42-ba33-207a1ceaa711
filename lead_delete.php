<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
require __DIR__ . '/vendor/autoload.php';
require __DIR__ . '/lead_delete.php';

header("Content-Type: application/json");


// Log file for debugging
//$logFile = 'slack_webhook.log';
// $logFile = 'myfile.txt';



// // Function to log messages
// function logMessage($message) {
//     global $logFile;
//     $timestamp = date('Y-m-d H:i:s');
//     file_put_contents($logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
// }

// logMessage('Webhook received');


$msg = "Webhook received.\n";
file_put_contents('myfile.txt', $msg, FILE_APPEND); // FILE_APPEND adds to the file instead of overwriting

// Function to parse Slack message into key-value pairs
// function parseMessage($text) {
//            $lines = explode("\n", $text);
//            $data = [];
//              foreach ($lines as $line) {
//               $parts = explode(" - ", $line, 2);
//               if (count($parts) == 2) {
//               $key = trim($parts[0]);
//               $value = trim($parts[1]);



//               //url filter
//                 if("Job URL"===$key){
//                     $cleaned_url="";
//                     $decode_url = html_entity_decode($value);
//                     $cleaned_url = str_replace(['<', '>'], '', $decode_url);
//                     $data[$key] = trim($cleaned_url, "<>");
//                 }else if("Lead_id"===$key){
//                     $data[$key] = trim($value);
//                     $data[$key] = intval($value);
//                 }
//                 else{
//                     $data[$key] = trim($value, "<>");
//                 }

//               //setting Spending Count
//               if("Spending"===$key){
//                 if( "Excellent"=== $value ){
//                     $data['Spending Count'] = 4;
//                 }else if( "Normal"=== $value ){
//                     $data['Spending Count'] = 2.5;
//                 }else if( "Low"=== $value ){
//                     $data['Spending Count'] = 1;
//                 }else{
//                     $data['Spending Count'] = 0;
//                 }

//             //setting Hired Count
//             if (isset($data["Lead Status Call"]) && $data["Lead Status Call"] === "Hired/Awaiting Start" || isset($data["Lead Status Chat"]) &&
//                  $data["Lead Status Chat"] === "Hired/Awaiting Start"){
//                 if( "Excellent" === $value){
//                     $data['Hired Count'] = 4;
//                 }else if( "Very High"=== $value || "High"=== $value){
//                     $data['Hired Count'] = 3;
//                 }
//                 else if( "Normal"=== $value){
//                     $data['Hired Count'] = 2.5;
//                 }else if( "Low"=== $value){
//                     $data['Hired Count'] = 1;
//                 }else if( "Very Low"=== $value || "Critically Low"=== $value){
//                     $data['Hired Count'] = 0.5;
//                 }else{
//                     $data['Hired Count'] = 0.5;
//                 }
//                 }
// 		else if (isset($data["Lead Status Call"]) && $data["Lead Status Call"] === "Hired" || isset($data["Lead Status Chat"]) &&
//                  $data["Lead Status Chat"] === "Hired") {
//                         $data['Hired Count'] = $data['Spending Count'];
//                 }
// 		else if (isset($data["Lead Status Chat"]) && $data["Lead Status Chat"] === "Not Converted") {
//                         $data['Hired Count'] = 0;
//                 }
//             }


//               }
//             }
//            return $data;
// }

// Get the raw POST data
$rawInput = file_get_contents('php://input');
logMessage('Raw input: ' . $rawInput);

// Decode JSON payload
$payload = json_decode($rawInput, true);

// Detailed payload logging
if ($payload) {
    logMessage('Payload type: ' . ($payload['type'] ?? 'not set'));
    if (isset($payload['event'])) {
        logMessage('Event type: ' . ($payload['event']['type'] ?? 'not set'));
    }
} else {
    logMessage('Failed to decode JSON or empty payload');
}

// Check if this is a verification challenge
if (isset($payload['challenge']) && isset($payload['type']) && $payload['type'] === 'url_verification') {
    logMessage('Challenge request received');
    header('Content-Type: application/json');
    echo json_encode(['challenge' => $payload['challenge']]);
    exit;
}



// Process event_callback (messages and other events)
if (isset($payload['type']) && $payload['type'] === 'event_callback' && isset($payload['event'])) {
    $event = $payload['event'];
    $eventType = $event['type'] ?? 'unknown';
    $subtype = $event['subtype'] ?? 'standard_message';
    $channelId = $event['channel'] ?? 'Unknown';
    $delete_row = false;
    logMessage("subtpyeevent type: $subtype ");
    if( "message_changed" === $subtype ){
            $event['text'] = "";
            $event = $event['message'];
    }else if( "message_deleted" === $subtype ){
            $event['text'] = "";
            $event = $event['previous_message'];
            $delete_row = true;
    }

    logMessage("text".json_encode($event));
    //validating post message format.
    $post_messaage_format_check = false;
    // Define required keys
    $requiredKeys = [
      'Lead_id',
      'Assigned to',
      'Upwork ID',
       'Type',
      'Client Name',
      'Spending',
      'Job Title',
      'Job URL',
      'Lead Status Chat',
      'Lead Status Call',
    ];

    // Parse key-value pairs
    $message_lines = explode("\n", $event['text']);
    $message_filed_data = [];

    foreach ($message_lines as $line) {
        if (preg_match('/^(.+?)\s*-\s*(.*)$/', $line, $match)) {
            $key = trim($match[1]);
            $value = trim($match[2]);
            $message_filed_data[$key] = $value;
        }
    }

    // Check for missing required keys
    $missingKeys = array_diff($requiredKeys, array_keys($message_filed_data));

    if (!empty($missingKeys)) {
        foreach ($missingKeys as $key) {
            echo "- $key\n";
            logMessage("$key is missing in slack post.");
        }
        $post_messaage_format_check = false;
    } else {
        logMessage("All required fields are present in slack post.");
       $post_messaage_format_check = true;
    }


    logMessage("Processing event type: $eventType");

    // Handle message events (standard messages, channel_join, etc.)
    if ($eventType === 'message' && $post_messaage_format_check) {



        $userId = $event['user'] ?? ($event['bot_id'] ?? 'Unknown');
        $text = $event['text'] ?? '';
        $timestamp = $event['ts'] ?? '';



        // Format data
        $messageData = [
            'channel_id' => $channelId,
            'user_id' => $userId,
            'message' => $text,
            'timestamp' => $timestamp,
            'subtype' => $subtype,
            'received_at' => date('Y-m-d H:i:s')
        ];

        // Convert to string for output
        $outputText = "Event Type - message ($subtype)\n";
        $outputText .= "Channel - $channelId\n";
        $outputText .= "User - $userId\n";
        $outputText .= "$text\n";
        $outputText .= "Time - $timestamp\n";
        $outputText .= "Received - " . date('Y-m-d H:i:s') . "\n";
        $outputText .= "Date - " . date("m/d/Y") . "\n";
        // Output to a file
        file_put_contents('slack_messages.txt', $outputText, FILE_APPEND);
	       $parsedData = parseMessage($outputText);

          // Map parsed data to column order

          // Load your service account credentials
          $client = new \Google_Client();
          $client->setApplicationName('Google Sheets API with PHP');
          $client->setScopes([\Google_Service_Sheets::SPREADSHEETS]);
          $client->setAuthConfig('odzleads-3fd4a44b7310.json');
          $client->setAccessType('offline');

          $service = new Google_Service_Sheets($client);

          // Define the spreadsheet ID and range
          $spreadsheetId = '1IIF_g_GYLvQzD0QULsM4_fdJJG-i0nDJU_rwQmNSG7U';
          $sheetName = 'Q3 July25- Sep25';
          $range = $sheetName.'!A2:O'; // or 'Q2Apr25June25!A18:A14' if specific

          // Data to append (each sub-array = one row)
          $columns = ["Lead_id", "BDE", "Upwork ID", "Type", "Client Name", "Lead Status Call", "Spending", "Job URL", "Job Title", "Assigned to", "Lead Status Chat", "Date" ,"Spending Count", "Hired Count", "Freelancer Name", "Remarks"];

          $rowData = [];
          foreach ($columns as $column) {
          	$rowData[] = $parsedData[$column] ?? ""; // Insert value or empty if missing
          }


          // getting lead_id
          $lead_id = trim($rowData[0]);
          $lead_id = intval($rowData[0]);

          $rowData1[] = $rowData;
          $filteredData = [];
          foreach ($rowData1 as $row) {
            $cleanedRow = [];

            foreach ($row as $cell) {
                // Remove "mailto:" and take only the part before the pipe "|"
                $cell = preg_replace('/^mailto:/i', '', $cell); // remove mailto:
                $cell = explode('|', $cell)[0]; // take only first part
                $decode_url = html_entity_decode($cell);
                $cleaned_url = str_replace(['<', '>'], '', $decode_url);
                $cell = trim($cleaned_url, "<>");

                // Now check if numeric and cast accordingly
                if (is_numeric($cell)) {
                    if ((float)$cell == (int)$cell) {
                        // It's a whole number
                        $cell = (int)$cell;
                    } else {
                        // It's a float — round to 1 decimal
                        $cell = round((float)$cell, 1);
                    }
                }

                $cleanedRow[] = trim($cell);
            }

            $filteredData[] = $cleanedRow;
        }
                  // getting lead_id
          $lead_id = trim($rowData[0]);
          $lead_id = intval($rowData[0]);

          logMessage("leadid-".$lead_id);
          logMessage("rowData1-".json_encode($filteredData));

          $body = new Google_Service_Sheets_ValueRange([
              'values' => $filteredData
          ]);


          $params = ['valueInputOption' => 'RAW'];


         //  Get all rows to find the row with matching message_id
         $get_all_rows = $service->spreadsheets_values->get($spreadsheetId, $range);
         $get_all_rows_values = $get_all_rows->getValues();
         $rowIndex = -1;

            foreach ($get_all_rows_values as $index => $row) {
                if (!empty($row[0]) && intval($row[0]) == $lead_id) {
                    $rowIndex = $index + 2; // Google Sheets uses 1-based indexing
                    break;
                }
            }


        //if row not found insert new
        if ($rowIndex == -1 && !$delete_row) {
                if(!lead_id_exists($lead_id)){
                    $result = $service->spreadsheets_values->append($spreadsheetId, $range, $body, $params);

                    echo "<br />Row added! ID: " . $result->getUpdates()->getUpdatedRange();

                    logMessage("final save".$result->getUpdates()->getUpdatedRange());
                }else{
                    logMessage("can to save beacuse lead_id exist in deleted_lead_ids file-".$lead_id);
                }
            }else{

                if( $delete_row ){
                    // Step 1: Get the numeric sheet ID from the sheet name
                    $spreadsheet = $service->spreadsheets->get($spreadsheetId);
                    $sheetId = null;

                    foreach ($spreadsheet->getSheets() as $sheet) {
                        if ($sheet->getProperties()->getTitle() === $sheetName) {
                            $sheetId = $sheet->getProperties()->getSheetId();
                            break;
                        }
                    }

                    if ($sheetId === null) {
                        logMessage("sheet not found to delete row");
                    }
                                        // Step 2: Prepare the request to delete that row
                    $deleteRequest = new Google_Service_Sheets_Request([
                        'deleteDimension' => [
                            'range' => [
                                'sheetId' => $sheetId,
                                'dimension' => 'ROWS',
                                'startIndex' => $rowIndex - 1, // Zero-based index
                                'endIndex' => $rowIndex        // Exclusive
                            ]
                        ]
                    ]);

                    $batchUpdateRequest = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
                        'requests' => [$deleteRequest]
                    ]);

                    // Step 3: Send the batch update request to delete the row
                    $service->spreadsheets->batchUpdate($spreadsheetId, $batchUpdateRequest);
                    logMessage("row deleted lead_id-".$lead_id);
                    delete_lead_id($lead_id);
                    logMessage("lead_id added to deleted_lead_ids file-".$lead_id);
                }else{
                    //update existing row
                    $updateRange = "$sheetName!A$rowIndex:R$rowIndex";
                    $body = new Google_Service_Sheets_ValueRange([
                        'values' => $filteredData
                    ]);

                  $params = ['valueInputOption' => 'RAW'];
                  $result = $service->spreadsheets_values->update($spreadsheetId, $updateRange, $body, $params);

                  logMessage("row update lead_id-".$lead_id);
                }
            }
            $leadStatus = strtolower(trim($parsedData['Lead Status Call'] ?? ''));
            if (!empty($leadStatus)) {
                $spreadsheet = $service->spreadsheets->get($spreadsheetId);
                $sheetId = null;
                foreach ($spreadsheet->getSheets() as $sheet) {
                    if ($sheet->getProperties()->getTitle() === $sheetName) {
                        $sheetId = $sheet->getProperties()->getSheetId();
                        break;
                    }
                }

                if ($sheetId !== null) {
                    $bgColor = $leadStatus === 'hired' ? ['green' => 1] : ['red' => 1];
                    $formatRequest = new Google_Service_Sheets_Request([
                        'repeatCell' => [
                            'range' => [
                                'sheetId' => $sheetId,
                                'startRowIndex' => $rowIndex - 1,
                                'endRowIndex' => $rowIndex,
                                'startColumnIndex' => 5,
                                'endColumnIndex' => 6
                            ],
                            'cell' => [
                                'userEnteredFormat' => [
                                    'backgroundColor' => $bgColor
                                ]
                            ],
                            'fields' => 'userEnteredFormat.backgroundColor'
                        ]
                    ]);

                    $batchRequest = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
                        'requests' => [$formatRequest]
                    ]);

                    $service->spreadsheets->batchUpdate($spreadsheetId, $batchRequest);
                    logMessage("Applied background color for Lead Status Call: $leadStatus on row $rowIndex");
                }
            }


    } else {
        // Log other event types
        logMessage("Received non-message event: $eventType - not processing");
    }
}

  ?>
